package com.bukuwarung.managers

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import com.bukuwarung.managers.local_notification.domain.DailyDueDateNotificationReceiver
import com.bukuwarung.session.SessionManager
import java.util.*
import java.util.concurrent.TimeUnit


class BootReceiver : BroadcastReceiver() {
    companion object {
        const val BOOT_RECEIVER_NAME = "BootReceiver"
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        if ((intent?.action == Intent.ACTION_BOOT_COMPLETED || intent?.action == BOOT_RECEIVER_NAME) && context != null) {
            SessionManager.getInstance().setDailyBroadcastRun()
            scheduleAlarms(context)
        }
    }

    private fun scheduleAlarms(context: Context) {
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.HOUR_OF_DAY, 8)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        if (hasRunToday()) {       //if the alarm has run this day
            calendar.add(Calendar.DATE, 1) //schedule it to run again starting tomorrow
        }
        val firstRunTime = calendar.timeInMillis
        val mgr = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
        val alarmIntent = Intent(context, DailyDueDateNotificationReceiver::class.java)
        val pendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PendingIntent.getBroadcast(context, 0, alarmIntent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE)
        } else {
            PendingIntent.getBroadcast(context, 0, alarmIntent, PendingIntent.FLAG_UPDATE_CURRENT)
        }
        mgr.cancel(pendingIntent)
        mgr.setRepeating(AlarmManager.RTC_WAKEUP, firstRunTime, AlarmManager.INTERVAL_DAY, pendingIntent)
    }

    private fun hasRunToday(): Boolean {
        val alarmLastRun = SessionManager.getInstance().dailyDueDateLastRun
        if (alarmLastRun == -1L) return false
        //check by comparing day, month and year
        return Date().time - alarmLastRun < TimeUnit.DAYS.toMillis(1)
    }
}
